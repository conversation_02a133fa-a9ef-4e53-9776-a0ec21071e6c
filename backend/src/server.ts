import express from 'express';
import helmet from 'helmet';
import dotenv from 'dotenv';
import analyzeRoutes from './routes/analyzeRoutes.js'; // Importera analyzeRoutes
import authRoutes from './routes/authRoutes.js'; // Importera authRoutes
import chatRoutes from './routes/chatRoutes.js'; // Importera chatRoutes
import fileRoutes from './routes/fileRoutes.js'; // Importera fileRoutes
import organizationRoutes from './routes/organizationRoutes.js'; // Importera organizationRoutes
import billingRoutes from './routes/billingRoutes.js'; // Importera billingRoutes
import cors from 'cors'; // Importera cors
import http from 'http'; // För Socket.IO
import { Server as SocketIOServer } from 'socket.io'; // För Socket.IO

dotenv.config();

const app = express();


// Skapa HTTP-server för Express-appen (behövs för Socket.IO)
const httpServer = http.createServer(app);

const allowedOrigins = [
  'http://localhost:3000', // Din Next.js frontend (när den körs på port 3000)
  'http://localhost:3001', // Din Next.js frontend
  // Lägg till din WeWeb produktions-URL här om den ska kunna nå backend
  // Exempel: 'https://app.flowlytics.com',
  // Lägg till din WeWeb utvecklings-/staging-URL här om den ska kunna nå backend
  // Exempel: 'https://dev.flowlytics.com',
  'http://localhost:8888', // Standardport för WeWeb lokal testning om du använder det
];

// Socket.IO-konfiguration
const io = new SocketIOServer(httpServer, {
  cors: {
    origin: allowedOrigins, // Återanvänd från Express CORS-konfig
    methods: ["GET", "POST"],
    credentials: true
  }
});
const port = process.env.PORT || 4000;

app.use(helmet()); // Basic security headers


const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // Tillåt anrop utan origin (t.ex. Postman, server-till-server) eller om origin är i listan
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS: Blocked origin - ${origin}`); // Logga blockerad origin
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true, // Viktigt för att skicka med cookies/auth headers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'], // Tillåt vanliga metoder
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'], // Tillåt vanliga headers
  optionsSuccessStatus: 200 // Vissa äldre webbläsare (IE11) har problem med 204
};

app.use(cors(corsOptions));

// Special handling for Stripe webhook - needs raw body
app.use('/api/billing/webhook', express.raw({ type: 'application/json' }));

app.use(express.json()); // Middleware to parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Middleware to parse URL-encoded bodies

// Montera analyzeRoutes under /api/analyze
app.use('/api/analyze', analyzeRoutes);

// Montera authRoutes under /api/auth
app.use('/api/auth', authRoutes);

// Montera chatRoutes under /api/chat
app.use('/api/chat', chatRoutes);

// Montera fileRoutes under /api/files
app.use('/api/files', fileRoutes);

// Montera organizationRoutes under /api/organization
app.use('/api/organization', organizationRoutes);

// Montera billingRoutes under /api/billing
app.use('/api/billing', billingRoutes);

// Socket.IO connection handler
io.on('connection', (socket) => {
  console.log('En klient anslöt via WebSocket:', socket.id);

  // Lyssna efter att klienten vill gå med i ett rum baserat på sitt userId
  socket.on('join_room', (data) => {
    const userId = data?.userId;
    if (userId) {
      socket.join(userId);
      console.log(`Socket ${socket.id} gick med i rum ${userId}`);
    } else {
      console.log(`Socket ${socket.id} försökte gå med i rum utan userId.`);
    }
  });

  socket.on('disconnect', () => {
    console.log('En klient kopplade från:', socket.id);
    // socket.io hanterar automatiskt borttagning från rum vid disconnect.
  });

  // Framtida händelselyssnare kan läggas till här (ersätter den gamla kommentaren och disconnect-logiken)
});


app.get('/', (req, res) => {
  res.send('Server is running!');
});

httpServer.listen(port, () => {
  console.log(`Server (HTTP och WebSocket) lyssnar på port ${port}`);
});

export { io }; // Exportera io-instansen så den kan användas i routes
export default app; // Export for potential testing
