# Server Configuration
PORT=4000

# Database (PostgreSQL via Prisma)
# Example: postgresql://user:password@host:port/database_name?schema=public
DATABASE_URL="postgresql://postgres:password@localhost:5432/mydb?schema=public"

# Redis (for BullMQ)
# Example: redis://localhost:6379
REDIS_HOST="localhost"
REDIS_PORT="6379"
# REDIS_PASSWORD="" # Uncomment and set if your Redis instance has a password
# REDIS_URL="redis://:password@host:port" # Alternative full URL

# Authentication
# Generate a secure random string for JWT signing
JWT_SECRET=your-super-secret-jwt-key-here

# OpenAI API (for Excel analysis)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# API Base URL (for frontend communication)
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000

# Stripe Configuration (REQUIRED for billing)
# Get these from your Stripe Dashboard: https://dashboard.stripe.com/apikeys

# Stripe Secret Key (starts with sk_test_ for test mode, sk_live_ for live mode)
STRIPE_SECRET_KEY=sk_test_51234567890abcdef_test_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12

# Stripe Webhook Secret (get from webhook endpoint settings)
# This is used to verify webhook signatures from Stripe
STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# Frontend URL (used for Stripe checkout redirects)
FRONTEND_URL=http://localhost:3000

# Development/Production Environment
NODE_ENV=development
