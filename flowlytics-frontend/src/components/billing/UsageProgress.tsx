'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Al<PERSON><PERSON>riangle, TrendingUp, Zap } from 'lucide-react';

interface UsageItem {
  used: number;
  limit: number;
  percentage: number;
}

interface UsageProgressProps {
  analyses: UsageItem;
  storage: UsageItem;
  users: UsageItem;
  planName: string;
  onUpgrade?: () => void;
  showUpgradeButton?: boolean;
}

export function UsageProgress({ 
  analyses, 
  storage, 
  users, 
  planName,
  onUpgrade,
  showUpgradeButton = true 
}: UsageProgressProps) {
  
  const formatBytes = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024);
    return gb < 1 ? `${Math.round(gb * 1024)} MB` : `${gb.toFixed(1)} GB`;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getUsageStatus = (percentage: number) => {
    if (percentage >= 100) return { label: 'Gräns nådd', variant: 'destructive' as const };
    if (percentage >= 90) return { label: 'Nästan full', variant: 'destructive' as const };
    if (percentage >= 75) return { label: 'Hög användning', variant: 'secondary' as const };
    return { label: 'Normal användning', variant: 'default' as const };
  };

  const hasHighUsage = analyses.percentage >= 75 || storage.percentage >= 75 || users.percentage >= 75;
  const hasMaxedOut = analyses.percentage >= 100 || storage.percentage >= 100 || users.percentage >= 100;

  return (
    <Card className={`bg-slate-800/50 backdrop-blur-sm border rounded-xl hover:bg-slate-800/60 transition-all duration-200 ${
      hasMaxedOut ? 'border-red-500/50 bg-red-900/20' :
      hasHighUsage ? 'border-yellow-500/50 bg-yellow-900/20' :
      'border-slate-700/50'
    }`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-white">
              <TrendingUp className="w-5 h-5 text-slate-400" />
              Användningsstatistik
            </CardTitle>
            <CardDescription className="text-slate-300">
              Nuvarande plan: {planName}
            </CardDescription>
          </div>
          {hasHighUsage && (
            <Badge variant={hasMaxedOut ? 'destructive' : 'secondary'}>
              {hasMaxedOut ? (
                <>
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Gräns nådd
                </>
              ) : (
                <>
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Hög användning
                </>
              )}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Analyses Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Analyser denna månad</span>
            <div className="flex items-center gap-2">
              <span className={`text-sm font-semibold ${getUsageColor(analyses.percentage)}`}>
                {analyses.used} / {analyses.limit}
              </span>
              <Badge variant={getUsageStatus(analyses.percentage).variant} className="text-xs">
                {getUsageStatus(analyses.percentage).label}
              </Badge>
            </div>
          </div>
          <Progress 
            value={Math.min(analyses.percentage, 100)} 
            className="h-2"
          />
          <p className="text-xs text-slate-600">
            {analyses.percentage}% av månadskvoten använd
          </p>
        </div>

        {/* Storage Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Lagringsutrymme</span>
            <div className="flex items-center gap-2">
              <span className={`text-sm font-semibold ${getUsageColor(storage.percentage)}`}>
                {formatBytes(storage.used)} / {formatBytes(storage.limit)}
              </span>
              <Badge variant={getUsageStatus(storage.percentage).variant} className="text-xs">
                {getUsageStatus(storage.percentage).label}
              </Badge>
            </div>
          </div>
          <Progress 
            value={Math.min(storage.percentage, 100)} 
            className="h-2"
          />
          <p className="text-xs text-slate-600">
            {storage.percentage}% av lagringsgränsen använd
          </p>
        </div>

        {/* Users Usage */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Aktiva användare</span>
            <div className="flex items-center gap-2">
              <span className={`text-sm font-semibold ${getUsageColor(users.percentage)}`}>
                {users.used} / {users.limit}
              </span>
              <Badge variant={getUsageStatus(users.percentage).variant} className="text-xs">
                {getUsageStatus(users.percentage).label}
              </Badge>
            </div>
          </div>
          <Progress 
            value={Math.min(users.percentage, 100)} 
            className="h-2"
          />
          <p className="text-xs text-slate-600">
            {users.percentage}% av användargränsen nådd
          </p>
        </div>

        {/* Upgrade Prompt */}
        {hasHighUsage && showUpgradeButton && (
          <div className={`p-4 rounded-lg border ${
            hasMaxedOut 
              ? 'bg-red-50 border-red-200' 
              : 'bg-yellow-50 border-yellow-200'
          }`}>
            <div className="flex items-start gap-3">
              <div className={`p-1 rounded-full ${
                hasMaxedOut ? 'bg-red-100' : 'bg-yellow-100'
              }`}>
                {hasMaxedOut ? (
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                ) : (
                  <Zap className="w-4 h-4 text-yellow-600" />
                )}
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-sm">
                  {hasMaxedOut 
                    ? 'Du har nått din användningsgräns' 
                    : 'Du närmar dig din användningsgräns'
                  }
                </h4>
                <p className="text-sm text-slate-600 mt-1">
                  {hasMaxedOut 
                    ? 'Uppgradera din plan för att fortsätta använda Flowlytics utan begränsningar.'
                    : 'Överväg att uppgradera din plan för att få mer kapacitet och undvika avbrott.'
                  }
                </p>
                <Button 
                  onClick={onUpgrade}
                  size="sm" 
                  className="mt-3"
                  variant={hasMaxedOut ? 'default' : 'outline'}
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Uppgradera Plan
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
