/**
 * Environment variable validation utility
 * Validates that all required environment variables are set
 */

interface EnvConfig {
  required: string[];
  optional: string[];
}

const ENV_CONFIG: EnvConfig = {
  required: [
    'DATABASE_URL',
    'JWT_SECRET',
    'OPENAI_API_KEY',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET'
  ],
  optional: [
    'PORT',
    'REDIS_HOST',
    'REDIS_PORT',
    'FRONTEND_URL',
    'NODE_ENV'
  ]
};

export function validateEnvironment(): void {
  console.log('🔧 Validating environment variables...');
  
  const missing: string[] = [];
  const warnings: string[] = [];

  // Check required variables
  for (const envVar of ENV_CONFIG.required) {
    if (!process.env[envVar]) {
      missing.push(envVar);
    } else {
      console.log(`✅ ${envVar} is configured`);
    }
  }

  // Check optional variables
  for (const envVar of ENV_CONFIG.optional) {
    if (!process.env[envVar]) {
      warnings.push(envVar);
    } else {
      console.log(`✅ ${envVar} is configured`);
    }
  }

  // Report warnings for optional variables
  if (warnings.length > 0) {
    console.log('\n⚠️  Optional environment variables not set:');
    warnings.forEach(envVar => {
      console.log(`   - ${envVar}`);
    });
  }

  // Report errors for required variables
  if (missing.length > 0) {
    console.error('\n❌ Missing required environment variables:');
    missing.forEach(envVar => {
      console.error(`   - ${envVar}`);
    });
    
    console.error('\n📝 Please add these to your .env file:');
    console.error('\nFor Stripe integration:');
    console.error('STRIPE_SECRET_KEY=sk_test_... (from https://dashboard.stripe.com/apikeys)');
    console.error('STRIPE_WEBHOOK_SECRET=whsec_... (from webhook endpoint settings)');
    
    console.error('\nFor database:');
    console.error('DATABASE_URL=postgresql://user:password@localhost:5432/database');
    
    console.error('\nFor authentication:');
    console.error('JWT_SECRET=your-secret-key');
    
    console.error('\nFor OpenAI:');
    console.error('OPENAI_API_KEY=sk-...');
    
    throw new Error(`Missing ${missing.length} required environment variable(s)`);
  }

  console.log('\n✅ All required environment variables are configured');
}

export function validateStripeConfig(): void {
  const stripeKey = process.env.STRIPE_SECRET_KEY;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!stripeKey) {
    throw new Error('STRIPE_SECRET_KEY is required for Stripe integration');
  }

  if (!webhookSecret) {
    console.warn('⚠️  STRIPE_WEBHOOK_SECRET not set - webhook verification will fail');
  }

  // Validate key format
  if (!stripeKey.startsWith('sk_')) {
    throw new Error('STRIPE_SECRET_KEY must start with "sk_"');
  }

  // Check if using test or live key
  const isTestKey = stripeKey.startsWith('sk_test_');
  const isLiveKey = stripeKey.startsWith('sk_live_');

  if (isTestKey) {
    console.log('🧪 Using Stripe TEST mode');
  } else if (isLiveKey) {
    console.log('🚀 Using Stripe LIVE mode');
  } else {
    console.warn('⚠️  Unknown Stripe key format');
  }

  console.log('✅ Stripe configuration validated');
}

export function getEnvWithDefault(key: string, defaultValue: string): string {
  return process.env[key] || defaultValue;
}

export function getRequiredEnv(key: string): string {
  const value = process.env[key];
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  return value;
}
