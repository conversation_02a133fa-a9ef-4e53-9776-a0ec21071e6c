'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Zap, Crown, Building } from 'lucide-react';
import { api } from '@/lib/api';
import { createCheckoutSession, formatPrice } from '@/lib/stripe';
import { toast } from 'sonner';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  analysisLimit: number;
  storageLimit: number;
  userLimit: number;
  features: string[];
}

interface SubscriptionPlansProps {
  currentPlan?: string;
  onPlanSelect?: (planId: string) => void;
  showCurrentBadge?: boolean;
}

const planIcons = {
  free: Zap,
  basic: Check,
  pro: Crown,
  enterprise: Building
};

const planColors = {
  free: 'bg-slate-700/50 text-slate-300 border-slate-600/50',
  basic: 'bg-blue-900/30 text-blue-300 border-blue-700/50',
  pro: 'bg-purple-900/30 text-purple-300 border-purple-700/50',
  enterprise: 'bg-orange-900/30 text-orange-300 border-orange-700/50'
};

export function SubscriptionPlans({ 
  currentPlan, 
  onPlanSelect, 
  showCurrentBadge = true 
}: SubscriptionPlansProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const response = await api.get('/billing/plans');
      if (response.success) {
        setPlans(response.plans);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast.error('Kunde inte hämta subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = async (planId: string) => {
    if (processingPlan || planId === currentPlan) return;

    setProcessingPlan(planId);

    try {
      if (onPlanSelect) {
        await onPlanSelect(planId);
      } else {
        // Default behavior - use Stripe checkout service
        await createCheckoutSession(planId);
      }
    } catch (error) {
      console.error('Error selecting plan:', error);
      toast.error('Kunde inte välja plan');
    } finally {
      setProcessingPlan(null);
    }
  };

  const formatPlanPrice = (price: number, currency: string, interval: string) => {
    if (price === 0) return 'Gratis';
    return `${formatPrice(price, currency)}/${interval === 'month' ? 'månad' : 'år'}`;
  };

  const formatStorage = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024);
    return `${gb}GB`;
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl">
            <CardHeader>
              <div className="h-6 bg-slate-700 rounded"></div>
              <div className="h-4 bg-slate-700 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-8 bg-slate-700 rounded"></div>
                <div className="h-4 bg-slate-700 rounded"></div>
                <div className="h-4 bg-slate-700 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {plans.map((plan) => {
        const Icon = planIcons[plan.id as keyof typeof planIcons] || Check;
        const isCurrentPlan = currentPlan === plan.id;
        const isPopular = plan.id === 'pro';
        
        return (
          <Card 
            key={plan.id} 
            className={`relative transition-all duration-200 hover:shadow-lg ${
              isCurrentPlan ? 'ring-2 ring-blue-500' : ''
            } ${isPopular ? 'border-purple-200 shadow-md' : ''}`}
          >
            {isPopular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-purple-600 text-white">Populär</Badge>
              </div>
            )}
            
            <CardHeader className="text-center">
              <div className="flex items-center justify-center mb-2">
                <div className={`p-2 rounded-full ${planColors[plan.id as keyof typeof planColors]}`}>
                  <Icon className="w-6 h-6" />
                </div>
              </div>
              
              <CardTitle className="flex items-center justify-center gap-2">
                {plan.name}
                {isCurrentPlan && showCurrentBadge && (
                  <Badge variant="secondary" className="text-xs">Nuvarande</Badge>
                )}
              </CardTitle>
              
              <CardDescription className="text-2xl font-bold text-slate-900">
                {formatPlanPrice(plan.price, plan.currency, plan.interval)}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Analyser per månad:</span>
                  <span className="font-medium">{plan.analysisLimit}</span>
                </div>
                <div className="flex justify-between">
                  <span>Lagring:</span>
                  <span className="font-medium">{formatStorage(plan.storageLimit)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Användare:</span>
                  <span className="font-medium">{plan.userLimit}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Funktioner:</h4>
                <ul className="space-y-1 text-sm text-slate-600">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              
              <Button
                onClick={() => handlePlanSelect(plan.id)}
                disabled={isCurrentPlan || processingPlan === plan.id}
                className={`w-full ${
                  isPopular 
                    ? 'bg-purple-600 hover:bg-purple-700' 
                    : isCurrentPlan 
                      ? 'bg-slate-400' 
                      : ''
                }`}
                variant={isCurrentPlan ? 'secondary' : 'default'}
              >
                {processingPlan === plan.id ? (
                  'Bearbetar...'
                ) : isCurrentPlan ? (
                  'Nuvarande plan'
                ) : plan.price === 0 ? (
                  'Välj gratis plan'
                ) : (
                  'Uppgradera'
                )}
              </Button>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
