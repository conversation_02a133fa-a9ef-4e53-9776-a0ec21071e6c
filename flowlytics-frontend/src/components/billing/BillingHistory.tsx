'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Receipt, 
  Download, 
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  ExternalLink
} from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface LocalUsageRecord {
  month: string;
  amount: number;
  currency: string;
  billedAt: string;
}

interface StripeInvoice {
  id: string;
  amount_paid: number;
  amount_due: number;
  currency: string;
  status: string;
  created: number;
  invoice_pdf?: string;
  hosted_invoice_url?: string;
  period_start: number;
  period_end: number;
}

interface BillingHistory {
  local: LocalUsageRecord[];
  stripe: StripeInvoice[];
}

export function BillingHistory() {
  const [history, setHistory] = useState<BillingHistory | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchBillingHistory();
  }, []);

  const fetchBillingHistory = async () => {
    try {
      const response = await api.get('/billing/history');
      if (response.success) {
        setHistory(response.history);
      }
    } catch (error) {
      console.error('Error fetching billing history:', error);
      toast.error('Kunde inte hämta billing history');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp: number | string) => {
    const date = typeof timestamp === 'number' ? new Date(timestamp * 1000) : new Date(timestamp);
    return date.toLocaleDateString('sv-SE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatAmount = (amount: number, currency: string) => {
    return `${amount} ${currency.toUpperCase()}`;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Betald
          </Badge>
        );
      case 'open':
        return (
          <Badge className="bg-blue-100 text-blue-800">
            <Clock className="w-3 h-3 mr-1" />
            Öppen
          </Badge>
        );
      case 'void':
      case 'uncollectible':
        return (
          <Badge className="bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            Annullerad
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            {status}
          </Badge>
        );
    }
  };

  const handleDownloadInvoice = (invoice: StripeInvoice) => {
    if (invoice.invoice_pdf) {
      window.open(invoice.invoice_pdf, '_blank');
    } else if (invoice.hosted_invoice_url) {
      window.open(invoice.hosted_invoice_url, '_blank');
    } else {
      toast.error('Faktura PDF inte tillgänglig');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            Billing History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded animate-pulse">
                <div className="space-y-2">
                  <div className="h-4 bg-slate-200 rounded w-32"></div>
                  <div className="h-3 bg-slate-200 rounded w-24"></div>
                </div>
                <div className="h-6 bg-slate-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!history || (history.local.length === 0 && history.stripe.length === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5" />
            Billing History
          </CardTitle>
          <CardDescription>
            Din faktureringshistorik visas här
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Receipt className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600">Ingen billing history tillgänglig än</p>
            <p className="text-sm text-slate-500 mt-2">
              Fakturor och betalningar kommer att visas här när de blir tillgängliga
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Receipt className="w-5 h-5" />
          Billing History
        </CardTitle>
        <CardDescription>
          Översikt över dina betalningar och fakturor
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Stripe Invoices */}
          {history.stripe.length > 0 && (
            <div>
              <h3 className="font-medium mb-4 flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Fakturor
              </h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Datum</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead>Belopp</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Åtgärder</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.stripe.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell>
                        {formatDate(invoice.created)}
                      </TableCell>
                      <TableCell className="text-sm text-slate-600">
                        {formatDate(invoice.period_start)} - {formatDate(invoice.period_end)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatAmount(invoice.amount_paid / 100, invoice.currency)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(invoice.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {(invoice.invoice_pdf || invoice.hosted_invoice_url) && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDownloadInvoice(invoice)}
                            >
                              <Download className="w-4 h-4 mr-1" />
                              PDF
                            </Button>
                          )}
                          {invoice.hosted_invoice_url && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(invoice.hosted_invoice_url, '_blank')}
                            >
                              <ExternalLink className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Local Usage Records */}
          {history.local.length > 0 && (
            <div>
              <h3 className="font-medium mb-4 flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                Användningshistorik
              </h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Månad</TableHead>
                    <TableHead>Belopp</TableHead>
                    <TableHead>Betald</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history.local.map((record, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        {new Date(record.month + '-01').toLocaleDateString('sv-SE', {
                          year: 'numeric',
                          month: 'long'
                        })}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatAmount(record.amount, record.currency)}
                      </TableCell>
                      <TableCell>
                        {formatDate(record.billedAt)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
