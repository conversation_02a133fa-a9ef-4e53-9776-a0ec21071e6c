'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CreditCard, 
  Calendar, 
  TrendingUp, 
  Users, 
  Database, 
  BarChart3,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { api } from '@/lib/api';
import { getSubscriptionStatusText, getSubscriptionStatusColor } from '@/lib/stripe';
import { toast } from 'sonner';

interface UsageStats {
  currentPlan: {
    id: string;
    name: string;
    price: number;
    currency: string;
  };
  usage: {
    analyses: {
      used: number;
      limit: number;
      percentage: number;
    };
    storage: {
      used: number;
      limit: number;
      percentage: number;
    };
    users: {
      used: number;
      limit: number;
      percentage: number;
    };
  };
  billingInfo?: {
    amount: number;
    currency: string;
    billedAt: string;
  };
}

interface Subscription {
  id: string;
  planId: string;
  status: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  plan: {
    name: string;
    price: number;
    currency: string;
  };
}

export function BillingDashboard() {
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [usageResponse, subscriptionResponse] = await Promise.all([
        api.get('/billing/usage'),
        api.get('/billing/subscription')
      ]);

      if (usageResponse.success) {
        setUsageStats(usageResponse.usage);
      }

      if (subscriptionResponse.success) {
        setSubscription(subscriptionResponse.subscription);
      }
    } catch (error) {
      console.error('Error fetching billing data:', error);
      toast.error('Kunde inte hämta billing information');
    } finally {
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024);
    return gb < 1 ? `${Math.round(gb * 1024)} MB` : `${gb.toFixed(1)} GB`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sv-SE');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'past_due': return <AlertTriangle className="w-4 h-4" />;
      case 'cancelled': return <AlertTriangle className="w-4 h-4" />;
      default: return <CheckCircle className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                <div className="h-6 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-2 bg-slate-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!usageStats) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p>Kunde inte ladda billing information</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Subscription */}
      <Card className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl hover:bg-slate-800/60 transition-all duration-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <CreditCard className="w-5 h-5 text-slate-400" />
            Nuvarande Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white">{usageStats.currentPlan.name}</h3>
              <p className="text-slate-300">
                {usageStats.currentPlan.price === 0
                  ? 'Gratis plan'
                  : `${usageStats.currentPlan.price} ${usageStats.currentPlan.currency.toUpperCase()}/månad`
                }
              </p>
              {subscription && (
                <div className="flex items-center gap-2 mt-2">
                  <Badge className={getSubscriptionStatusColor(subscription.status)}>
                    {getStatusIcon(subscription.status)}
                    {getSubscriptionStatusText(subscription.status)}
                  </Badge>
                  {subscription.status === 'active' && (
                    <span className="text-sm text-slate-400">
                      Förnyas {formatDate(subscription.currentPeriodEnd)}
                    </span>
                  )}
                </div>
              )}
            </div>
            <Button variant="outline">
              Hantera Subscription
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl hover:bg-slate-800/60 transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">Analyser</CardTitle>
            <BarChart3 className="h-4 w-4 text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageStats.usage.analyses.used} / {usageStats.usage.analyses.limit}
            </div>
            <Progress 
              value={usageStats.usage.analyses.percentage} 
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-2">
              {usageStats.usage.analyses.percentage}% använt denna månad
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Lagring</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatBytes(usageStats.usage.storage.used)} / {formatBytes(usageStats.usage.storage.limit)}
            </div>
            <Progress 
              value={usageStats.usage.storage.percentage} 
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-2">
              {usageStats.usage.storage.percentage}% använt
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Användare</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {usageStats.usage.users.used} / {usageStats.usage.users.limit}
            </div>
            <Progress 
              value={usageStats.usage.users.percentage} 
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-2">
              {usageStats.usage.users.percentage}% av gränsen
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Billing Information */}
      {usageStats.billingInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Senaste Faktura
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-lg font-semibold">
                  {usageStats.billingInfo.amount} {usageStats.billingInfo.currency}
                </p>
                <p className="text-slate-600">
                  Betald {formatDate(usageStats.billingInfo.billedAt)}
                </p>
              </div>
              <Button variant="outline">
                Visa Fakturor
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
