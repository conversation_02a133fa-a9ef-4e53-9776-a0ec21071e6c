import { loadStripe, Stripe } from '@stripe/stripe-js';

// Singleton pattern för Stripe instance
let stripePromise: Promise<Stripe | null>;

const getStripe = () => {
  if (!stripePromise) {
    const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    
    if (!publishableKey) {
      console.error('Stripe publishable key is missing');
      return Promise.resolve(null);
    }
    
    stripePromise = loadStripe(publishableKey);
  }
  return stripePromise;
};

export default getStripe;

/**
 * Redirect to Stripe Checkout
 */
export const redirectToCheckout = async (sessionId: string) => {
  const stripe = await getStripe();
  
  if (!stripe) {
    throw new Error('Stripe failed to load');
  }

  const { error } = await stripe.redirectToCheckout({
    sessionId,
  });

  if (error) {
    throw error;
  }
};

/**
 * Create checkout session and redirect
 */
export const createCheckoutSession = async (planId: string) => {
  try {
    const response = await fetch('/api/billing/checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
      },
      body: JSON.stringify({ planId }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to create checkout session');
    }

    if (data.success && data.sessionId) {
      await redirectToCheckout(data.sessionId);
    } else if (data.success && data.url) {
      // Direct redirect URL from Stripe
      window.location.href = data.url;
    } else {
      throw new Error('Invalid checkout session response');
    }
  } catch (error) {
    console.error('Checkout error:', error);
    throw error;
  }
};

/**
 * Format price for display
 */
export const formatPrice = (amount: number, currency: string = 'sek') => {
  return new Intl.NumberFormat('sv-SE', {
    style: 'currency',
    currency: currency.toUpperCase(),
    minimumFractionDigits: 0,
  }).format(amount);
};

/**
 * Stripe webhook event types
 */
export const STRIPE_WEBHOOK_EVENTS = {
  CHECKOUT_SESSION_COMPLETED: 'checkout.session.completed',
  CUSTOMER_SUBSCRIPTION_CREATED: 'customer.subscription.created',
  CUSTOMER_SUBSCRIPTION_UPDATED: 'customer.subscription.updated',
  CUSTOMER_SUBSCRIPTION_DELETED: 'customer.subscription.deleted',
  INVOICE_PAYMENT_SUCCEEDED: 'invoice.payment_succeeded',
  INVOICE_PAYMENT_FAILED: 'invoice.payment_failed',
  CUSTOMER_SUBSCRIPTION_TRIAL_WILL_END: 'customer.subscription.trial_will_end',
} as const;

/**
 * Subscription status mapping
 */
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  PAST_DUE: 'past_due',
  UNPAID: 'unpaid',
  CANCELED: 'canceled',
  INCOMPLETE: 'incomplete',
  INCOMPLETE_EXPIRED: 'incomplete_expired',
  TRIALING: 'trialing',
} as const;

/**
 * Get user-friendly status text
 */
export const getSubscriptionStatusText = (status: string) => {
  switch (status) {
    case SUBSCRIPTION_STATUS.ACTIVE:
      return 'Aktiv';
    case SUBSCRIPTION_STATUS.PAST_DUE:
      return 'Förfallen';
    case SUBSCRIPTION_STATUS.UNPAID:
      return 'Obetald';
    case SUBSCRIPTION_STATUS.CANCELED:
      return 'Avbruten';
    case SUBSCRIPTION_STATUS.INCOMPLETE:
      return 'Ofullständig';
    case SUBSCRIPTION_STATUS.INCOMPLETE_EXPIRED:
      return 'Utgången';
    case SUBSCRIPTION_STATUS.TRIALING:
      return 'Testperiod';
    default:
      return status;
  }
};

/**
 * Get status color class
 */
export const getSubscriptionStatusColor = (status: string) => {
  switch (status) {
    case SUBSCRIPTION_STATUS.ACTIVE:
    case SUBSCRIPTION_STATUS.TRIALING:
      return 'bg-green-100 text-green-800';
    case SUBSCRIPTION_STATUS.PAST_DUE:
    case SUBSCRIPTION_STATUS.UNPAID:
      return 'bg-yellow-100 text-yellow-800';
    case SUBSCRIPTION_STATUS.CANCELED:
    case SUBSCRIPTION_STATUS.INCOMPLETE_EXPIRED:
      return 'bg-red-100 text-red-800';
    case SUBSCRIPTION_STATUS.INCOMPLETE:
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-slate-100 text-slate-800';
  }
};
