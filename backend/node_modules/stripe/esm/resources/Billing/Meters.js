// File generated from our OpenAPI spec
import { StripeResource } from '../../StripeResource.js';
const stripeMethod = StripeResource.method;
export const Meters = StripeResource.extend({
    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters' }),
    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/billing/meters/{id}' }),
    update: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters/{id}' }),
    list: stripeMethod({
        method: 'GET',
        fullPath: '/v1/billing/meters',
        methodType: 'list',
    }),
    deactivate: stripeMethod({
        method: 'POST',
        fullPath: '/v1/billing/meters/{id}/deactivate',
    }),
    listEventSummaries: stripeMethod({
        method: 'GET',
        fullPath: '/v1/billing/meters/{id}/event_summaries',
        methodType: 'list',
    }),
    reactivate: stripeMethod({
        method: 'POST',
        fullPath: '/v1/billing/meters/{id}/reactivate',
    }),
});
