import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { StripeService, SUBSCRIPTION_PLANS } from '../services/stripeService.js';
import Stripe from 'stripe';

// Mock Stripe
jest.mock('stripe');
const MockedStripe = Stripe as jest.MockedClass<typeof Stripe>;

// Mock Prisma
const mockPrisma = {
  organization: {
    findUnique: jest.fn(),
    update: jest.fn(),
  },
  subscription: {
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
  },
  usageRecord: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    upsert: jest.fn(),
  },
  user: {
    findMany: jest.fn(),
  },
  analysis: {
    findMany: jest.fn(),
  },
};

// Mock Stripe instance methods
const mockStripeInstance = {
  customers: {
    create: jest.fn(),
  },
  subscriptions: {
    create: jest.fn(),
    retrieve: jest.fn(),
    update: jest.fn(),
    cancel: jest.fn(),
  },
  prices: {
    create: jest.fn(),
    list: jest.fn(),
  },
  products: {
    create: jest.fn(),
  },
  invoices: {
    list: jest.fn(),
  },
  checkout: {
    sessions: {
      create: jest.fn(),
    },
  },
  webhooks: {
    constructEvent: jest.fn(),
  },
};

MockedStripe.mockImplementation(() => mockStripeInstance as any);

// Mock environment variables
process.env.STRIPE_SECRET_KEY = 'sk_test_123';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_123';

describe('StripeService', () => {
  let stripeService: StripeService;

  beforeEach(() => {
    stripeService = new StripeService();
    jest.clearAllMocks();
  });

  describe('getAvailablePlans', () => {
    it('should return all subscription plans', () => {
      const plans = stripeService.getAvailablePlans();
      
      expect(plans).toHaveLength(4);
      expect(plans).toEqual(Object.values(SUBSCRIPTION_PLANS));
      expect(plans[0]).toHaveProperty('id', 'free');
      expect(plans[1]).toHaveProperty('id', 'basic');
      expect(plans[2]).toHaveProperty('id', 'pro');
      expect(plans[3]).toHaveProperty('id', 'enterprise');
    });
  });

  describe('createCustomer', () => {
    it('should create a Stripe customer and update organization', async () => {
      const mockCustomer = {
        id: 'cus_123',
        email: '<EMAIL>',
        name: 'Test Org',
      };

      mockStripeInstance.customers.create.mockResolvedValue(mockCustomer);
      mockPrisma.organization.update.mockResolvedValue({});

      const result = await stripeService.createCustomer(
        'org-123',
        '<EMAIL>',
        'Test Org'
      );

      expect(mockStripeInstance.customers.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'Test Org',
        metadata: {
          organizationId: 'org-123'
        }
      });

      expect(result).toEqual(mockCustomer);
    });
  });

  describe('createCheckoutSession', () => {
    it('should create a checkout session for paid plan', async () => {
      const mockOrganization = {
        id: 'org-123',
        name: 'Test Org',
        billingEmail: '<EMAIL>',
        stripeCustomerId: null,
      };

      const mockSession = {
        id: 'cs_123',
        url: 'https://checkout.stripe.com/pay/cs_123',
      };

      mockPrisma.organization.findUnique.mockResolvedValue(mockOrganization);
      mockStripeInstance.prices.list.mockResolvedValue({ data: [] });
      mockStripeInstance.products.create.mockResolvedValue({ id: 'prod_123' });
      mockStripeInstance.prices.create.mockResolvedValue({ id: 'price_123' });
      mockStripeInstance.checkout.sessions.create.mockResolvedValue(mockSession);

      const result = await stripeService.createCheckoutSession(
        'org-123',
        'basic',
        'https://success.com',
        'https://cancel.com'
      );

      expect(mockStripeInstance.checkout.sessions.create).toHaveBeenCalledWith({
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [
          {
            price: 'price_123',
            quantity: 1,
          },
        ],
        customer_email: '<EMAIL>',
        metadata: {
          organizationId: 'org-123',
          planId: 'basic'
        },
        success_url: 'https://success.com',
        cancel_url: 'https://cancel.com',
        subscription_data: {
          metadata: {
            organizationId: 'org-123',
            planId: 'basic'
          }
        }
      });

      expect(result).toEqual(mockSession);
    });

    it('should throw error for free plan checkout', async () => {
      const mockOrganization = {
        id: 'org-123',
        name: 'Test Org',
        billingEmail: '<EMAIL>',
      };

      mockPrisma.organization.findUnique.mockResolvedValue(mockOrganization);

      await expect(
        stripeService.createCheckoutSession(
          'org-123',
          'free',
          'https://success.com',
          'https://cancel.com'
        )
      ).rejects.toThrow('Invalid plan for checkout');
    });

    it('should throw error for invalid plan', async () => {
      const mockOrganization = {
        id: 'org-123',
        name: 'Test Org',
        billingEmail: '<EMAIL>',
      };

      mockPrisma.organization.findUnique.mockResolvedValue(mockOrganization);

      await expect(
        stripeService.createCheckoutSession(
          'org-123',
          'invalid-plan',
          'https://success.com',
          'https://cancel.com'
        )
      ).rejects.toThrow('Invalid plan for checkout');
    });
  });

  describe('handleWebhook', () => {
    it('should handle checkout.session.completed event', async () => {
      const mockEvent = {
        type: 'checkout.session.completed',
        data: {
          object: {
            id: 'cs_123',
            subscription: 'sub_123',
            metadata: {
              organizationId: 'org-123',
              planId: 'basic'
            }
          }
        }
      };

      const mockSubscription = {
        id: 'sub_123',
        status: 'active',
        metadata: {
          organizationId: 'org-123',
          planId: 'basic'
        }
      };

      mockStripeInstance.webhooks.constructEvent.mockReturnValue(mockEvent);
      mockStripeInstance.subscriptions.retrieve.mockResolvedValue(mockSubscription);
      mockPrisma.subscription.updateMany.mockResolvedValue({});
      mockPrisma.organization.update.mockResolvedValue({});

      await stripeService.handleWebhook('test-signature', 'test-payload');

      expect(mockStripeInstance.webhooks.constructEvent).toHaveBeenCalledWith(
        'test-payload',
        'test-signature',
        'whsec_test_123'
      );

      expect(mockPrisma.organization.update).toHaveBeenCalledWith({
        where: { id: 'org-123' },
        data: {
          subscriptionStatus: 'active',
          subscriptionTier: 'basic'
        }
      });
    });

    it('should handle subscription.updated event', async () => {
      const mockEvent = {
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_123',
            status: 'active',
            current_period_start: 1640995200,
            current_period_end: 1643673600,
            metadata: {
              organizationId: 'org-123'
            }
          }
        }
      };

      mockStripeInstance.webhooks.constructEvent.mockReturnValue(mockEvent);
      mockPrisma.subscription.updateMany.mockResolvedValue({});
      mockPrisma.organization.update.mockResolvedValue({});

      await stripeService.handleWebhook('test-signature', 'test-payload');

      expect(mockPrisma.subscription.updateMany).toHaveBeenCalledWith({
        where: {
          organizationId: 'org-123',
          stripeSubscriptionId: 'sub_123'
        },
        data: {
          status: 'active',
          currentPeriodStart: new Date(1640995200 * 1000),
          currentPeriodEnd: new Date(1643673600 * 1000)
        }
      });
    });

    it('should handle invalid webhook signature', async () => {
      mockStripeInstance.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      await expect(
        stripeService.handleWebhook('invalid-signature', 'test-payload')
      ).rejects.toThrow('Webhook signature verification failed: Invalid signature');
    });
  });

  describe('cancelSubscription', () => {
    it('should cancel subscription and update to free plan', async () => {
      const mockOrganization = {
        id: 'org-123',
        subscription: {
          stripeSubscriptionId: 'sub_123'
        }
      };

      mockPrisma.organization.findUnique.mockResolvedValue(mockOrganization);
      mockStripeInstance.subscriptions.cancel.mockResolvedValue({});
      mockPrisma.subscription.create.mockResolvedValue({});
      mockPrisma.organization.update.mockResolvedValue({});

      await stripeService.cancelSubscription('org-123');

      expect(mockStripeInstance.subscriptions.cancel).toHaveBeenCalledWith('sub_123');
      expect(mockPrisma.organization.update).toHaveBeenCalledWith({
        where: { id: 'org-123' },
        data: {
          subscriptionStatus: 'cancelled',
          subscriptionTier: 'free'
        }
      });
    });
  });

  describe('getUsageStats', () => {
    it('should return usage statistics', async () => {
      const mockOrganization = {
        id: 'org-123',
        storageUsed: BigInt(1000000),
        subscription: {
          planId: 'basic'
        },
        analyses: [{ id: '1' }, { id: '2' }],
        users: [{ id: 1 }, { id: 2 }, { id: 3 }]
      };

      const mockUsageRecord = {
        amount: 299,
        currency: 'SEK',
        billedAt: new Date()
      };

      mockPrisma.organization.findUnique.mockResolvedValue(mockOrganization);
      mockPrisma.usageRecord.findUnique.mockResolvedValue(mockUsageRecord);

      const result = await stripeService.getUsageStats('org-123');

      expect(result).toEqual({
        currentPlan: SUBSCRIPTION_PLANS.basic,
        usage: {
          analyses: {
            used: 2,
            limit: 100,
            percentage: 2
          },
          storage: {
            used: 1000000,
            limit: 5368709120,
            percentage: 0
          },
          users: {
            used: 3,
            limit: 10,
            percentage: 30
          }
        },
        billingInfo: mockUsageRecord
      });
    });
  });
});
