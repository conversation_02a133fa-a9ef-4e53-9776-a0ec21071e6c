{"name": "backend", "version": "1.0.0", "description": "Backend service", "main": "dist/server.js", "type": "module", "scripts": {"dev": "NODE_ENV=development tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "prettier --check .", "format": "prettier --write .", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:studio": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.15.0", "@types/decimal.js": "^0.0.32", "bcryptjs": "^3.0.2", "bullmq": "^5.8.2", "cors": "^2.8.5", "csv-parser": "^3.0.0", "decimal.js": "^10.5.0", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "ioredis": "^5.4.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "openai": "^5.1.1", "pdfkit": "^0.15.2", "qs": "^6.14.0", "socket.io": "^4.8.1", "stripe": "^18.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "@types/node": "^20.14.2", "@types/pdfkit": "^0.13.9", "@types/xlsx": "^0.0.35", "nodemon": "^3.1.3", "prettier": "^3.3.2", "prisma": "^5.15.0", "ts-node": "^10.9.2", "tsx": "^4.20.2", "typescript": "^5.4.5"}, "prisma": {"schema": "prisma/schema.prisma"}}