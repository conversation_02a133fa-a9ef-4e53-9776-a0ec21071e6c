import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import { PrismaClient } from '@prisma/client';
import { StripeService } from '../services/stripeService.js';
import billingRoutes from '../routes/billingRoutes.js';
import { protect } from '../middleware/authMiddleware.js';

// Mock dependencies
jest.mock('../services/stripeService.js');
jest.mock('../middleware/authMiddleware.js');
jest.mock('../middleware/tenantMiddleware.js');

const mockStripeService = StripeService as jest.MockedClass<typeof StripeService>;
const mockProtect = protect as jest.MockedFunction<typeof protect>;

// Mock Prisma
const mockPrisma = {
  organization: {
    findUnique: jest.fn(),
    update: jest.fn(),
  },
  subscription: {
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  usageRecord: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
  },
} as unknown as PrismaClient;

// Test app setup
const app = express();
app.use(express.json());

// Mock auth middleware to add user to request
mockProtect.mockImplementation((req: any, res, next) => {
  req.user = {
    userId: 1,
    organizationId: 'test-org-id',
    role: 'owner'
  };
  next();
});

// Mock tenant middleware
jest.mock('../middleware/tenantMiddleware.js', () => ({
  tenantMiddleware: (req: any, res: any, next: any) => next(),
  ensureSameOrganization: (req: any, res: any, next: any) => next(),
  requireRole: () => (req: any, res: any, next: any) => next(),
}));

app.use('/api/billing', billingRoutes);

describe('Billing Routes', () => {
  let mockStripeServiceInstance: jest.Mocked<StripeService>;

  beforeEach(() => {
    mockStripeServiceInstance = {
      getAvailablePlans: jest.fn(),
      getCurrentSubscription: jest.fn(),
      createCheckoutSession: jest.fn(),
      handleWebhook: jest.fn(),
      getBillingHistory: jest.fn(),
      cancelSubscription: jest.fn(),
      updateSubscription: jest.fn(),
      getUsageStats: jest.fn(),
    } as any;

    mockStripeService.mockImplementation(() => mockStripeServiceInstance);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/billing/plans', () => {
    it('should return available subscription plans', async () => {
      const mockPlans = [
        {
          id: 'free',
          name: 'Free',
          price: 0,
          currency: 'sek',
          interval: 'month',
          analysisLimit: 10,
          storageLimit: 1073741824,
          userLimit: 3,
          features: ['10 analyser per månad']
        }
      ];

      mockStripeServiceInstance.getAvailablePlans.mockReturnValue(mockPlans);

      const response = await request(app)
        .get('/api/billing/plans')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        plans: mockPlans
      });
    });

    it('should handle errors when fetching plans', async () => {
      mockStripeServiceInstance.getAvailablePlans.mockImplementation(() => {
        throw new Error('Service error');
      });

      const response = await request(app)
        .get('/api/billing/plans')
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        message: 'Kunde inte hämta subscription plans'
      });
    });
  });

  describe('GET /api/billing/subscription', () => {
    it('should return current subscription', async () => {
      const mockSubscription = {
        id: 'sub-123',
        planId: 'basic',
        status: 'active',
        plan: {
          name: 'Basic',
          price: 299,
          currency: 'sek'
        }
      };

      mockStripeServiceInstance.getCurrentSubscription.mockResolvedValue(mockSubscription);

      const response = await request(app)
        .get('/api/billing/subscription')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        subscription: mockSubscription
      });

      expect(mockStripeServiceInstance.getCurrentSubscription).toHaveBeenCalledWith('test-org-id');
    });

    it('should handle missing organization ID', async () => {
      // Mock request without organizationId
      mockProtect.mockImplementationOnce((req: any, res, next) => {
        req.user = { userId: 1, role: 'owner' }; // Missing organizationId
        next();
      });

      const response = await request(app)
        .get('/api/billing/subscription')
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        message: 'Organization ID saknas'
      });
    });
  });

  describe('POST /api/billing/checkout', () => {
    it('should create checkout session successfully', async () => {
      const mockSession = {
        id: 'cs_123',
        url: 'https://checkout.stripe.com/pay/cs_123'
      };

      mockStripeServiceInstance.createCheckoutSession.mockResolvedValue(mockSession);

      const response = await request(app)
        .post('/api/billing/checkout')
        .send({ planId: 'basic' })
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        sessionId: 'cs_123',
        url: 'https://checkout.stripe.com/pay/cs_123'
      });

      expect(mockStripeServiceInstance.createCheckoutSession).toHaveBeenCalledWith(
        'test-org-id',
        'basic',
        expect.stringContaining('/dashboard/billing/success'),
        expect.stringContaining('/dashboard/billing')
      );
    });

    it('should require planId', async () => {
      const response = await request(app)
        .post('/api/billing/checkout')
        .send({})
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        message: 'Plan ID krävs'
      });
    });
  });

  describe('POST /api/billing/webhook', () => {
    it('should handle webhook successfully', async () => {
      const mockPayload = JSON.stringify({ type: 'checkout.session.completed' });
      const mockSignature = 'whsec_test_signature';

      mockStripeServiceInstance.handleWebhook.mockResolvedValue(undefined);

      const response = await request(app)
        .post('/api/billing/webhook')
        .set('stripe-signature', mockSignature)
        .send(mockPayload)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        received: true
      });

      expect(mockStripeServiceInstance.handleWebhook).toHaveBeenCalledWith(
        mockSignature,
        expect.any(Object)
      );
    });

    it('should require stripe signature', async () => {
      const response = await request(app)
        .post('/api/billing/webhook')
        .send({ type: 'test' })
        .expect(400);

      expect(response.body).toEqual({
        success: false,
        message: 'Stripe signature saknas'
      });
    });
  });

  describe('GET /api/billing/usage', () => {
    it('should return usage statistics', async () => {
      const mockUsage = {
        currentPlan: {
          id: 'basic',
          name: 'Basic',
          price: 299,
          currency: 'sek'
        },
        usage: {
          analyses: { used: 5, limit: 100, percentage: 5 },
          storage: { used: 1000000, limit: 5368709120, percentage: 0.02 },
          users: { used: 2, limit: 10, percentage: 20 }
        }
      };

      mockStripeServiceInstance.getUsageStats.mockResolvedValue(mockUsage);

      const response = await request(app)
        .get('/api/billing/usage')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        usage: mockUsage
      });
    });
  });

  describe('POST /api/billing/cancel', () => {
    it('should cancel subscription successfully', async () => {
      mockStripeServiceInstance.cancelSubscription.mockResolvedValue(undefined);

      const response = await request(app)
        .post('/api/billing/cancel')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Subscription avbruten framgångsrikt'
      });

      expect(mockStripeServiceInstance.cancelSubscription).toHaveBeenCalledWith('test-org-id');
    });
  });

  describe('PUT /api/billing/subscription', () => {
    it('should update subscription successfully', async () => {
      const mockUpdatedSubscription = {
        id: 'sub-123',
        planId: 'pro',
        status: 'active'
      };

      mockStripeServiceInstance.updateSubscription.mockResolvedValue({
        success: true,
        plan: { id: 'pro', name: 'Pro' },
        subscription: mockUpdatedSubscription
      });

      const response = await request(app)
        .put('/api/billing/subscription')
        .send({ planId: 'pro' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Subscription uppdaterad framgångsrikt');

      expect(mockStripeServiceInstance.updateSubscription).toHaveBeenCalledWith(
        'test-org-id',
        'pro'
      );
    });
  });
});
