// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  // Notera: Denna output är specifik för din 'auth'-tjänst.
  // Om Excel-analys är en separat tjänst med egna node_modules,
  // kan detta behöva justeras eller en separat generator client block läggas till.
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Multi-tenant organization model
model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique // URL-friendly identifier
  domain      String?  @unique // Custom domain (optional)

  // Subscription & billing
  subscriptionTier    String   @default("free") // free, basic, pro, enterprise
  subscriptionStatus  String   @default("active") // active, suspended, cancelled, past_due
  billingEmail        String?

  // Stripe integration
  stripeCustomerId    String?  @unique

  // Usage tracking
  monthlyAnalyses     Int      @default(0)
  analysisLimit       Int      @default(10) // Based on subscription tier
  storageUsed         BigInt   @default(0) // In bytes
  storageLimit        BigInt   @default(**********) // 1GB default

  // Settings
  settings            Json?    // Organization-specific settings
  branding            Json?    // White-label branding options

  // Relations
  users               User[]
  analyses            Analysis[]
  files               File[]
  subscriptions       Subscription[]

  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@map("organizations")
}

model User {
  id             Int      @id @default(autoincrement())
  email          String   @unique
  name           String?
  password       String

  // Multi-tenant fields
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  role           String   @default("member") // owner, admin, member, viewer

  // User preferences
  preferences    Json?    // User-specific settings
  lastLoginAt    DateTime?
  isActive       Boolean  @default(true)

  posts          Post[]
  analyses       Analysis[] // Relation till analyser gjorda av användaren
  files          File[] // Relation to the File model

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("users")
}

model Post {
  id        Int      @id @default(autoincrement())
  title     String
  content   String?
  published Boolean  @default(false)
  author    User?    @relation(fields: [authorId], references: [id])
  authorId  Int?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Analysis {
  id               String    @id @default(uuid())
  originalFilename String
  status           String    // Exempel: "PENDING", "PROCESSING", "COMPLETED", "FAILED"
  analysisResult   Json?     // Lagrar resultatet från OpenAI
  errorMessage     String?   // För eventuella fel under analysen

  // Multi-tenant fields
  organizationId   String
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  userId           Int?
  user             User?     @relation(fields: [userId], references: [id])

  // Usage tracking
  tokensUsed       Int?      // OpenAI tokens consumed
  processingTime   Int?      // Processing time in milliseconds
  fileSize         BigInt?   // Original file size in bytes

  messages         ChatMessage[] // Relation till chattmeddelanden

  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  @@map("analyses")
}

model ChatMessage {
  id        String   @id @default(uuid())
  content   String
  role      String   // "user" or "assistant"
  
  analysisId String
  analysis  Analysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
}

model File {
  id             String   @id @default(cuid())
  name           String
  type           String
  size           BigInt   // File size in bytes
  status         String
  path           String   // Path to the file on the server's filesystem

  // Multi-tenant fields
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  userId         Int      // Match the User model's ID type
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  uploaded       DateTime @default(now())

  @@map("files")
}

// Subscription and billing models
model Subscription {
  id               String   @id @default(cuid())
  organizationId   String
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Subscription details
  tier             String   // free, basic, pro, enterprise
  status           String   // active, past_due, cancelled, unpaid
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime

  // Billing
  stripeCustomerId      String?
  stripeSubscriptionId  String?
  stripePriceId         String?

  // Usage limits
  analysisLimit    Int
  storageLimit     BigInt
  userLimit        Int

  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("subscriptions")
}

// Usage tracking for billing
model UsageRecord {
  id             String   @id @default(cuid())
  organizationId String

  // Usage metrics
  month          String   // YYYY-MM format
  analysesCount  Int      @default(0)
  storageUsed    BigInt   @default(0)
  tokensUsed     Int      @default(0)

  // Billing
  amount         Decimal? @db.Decimal(10, 2)
  currency       String   @default("SEK")
  billedAt       DateTime?

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([organizationId, month])
  @@map("usage_records")
}