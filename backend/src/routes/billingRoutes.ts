import { Router } from 'express';
import { protect } from '../middleware/authMiddleware.js';
import { tenantMiddleware, ensureSameOrganization, requireRole } from '../middleware/tenantMiddleware.js';
import {
  getSubscriptionPlans,
  getCurrentSubscription,
  createCheckoutSession,
  handleStripeWebhook,
  getBillingHistory,
  cancelSubscription,
  updateSubscription,
  getUsageStats
} from '../controllers/billingController.js';

const router = Router();

/**
 * GET /api/billing/plans
 * Hämtar alla tillgängliga subscription plans
 */
router.get('/plans', getSubscriptionPlans);

/**
 * GET /api/billing/subscription
 * Hämtar organisationens nuvarande subscription
 */
router.get('/subscription',
  protect,
  tenantMiddleware,
  ensureSameOrganization,
  getCurrentSubscription
);

/**
 * POST /api/billing/checkout
 * Skapar en Stripe Checkout session för subscription upgrade/change
 */
router.post('/checkout',
  protect,
  tenantMiddleware,
  ensureSameOrganization,
  requireRole(['owner', 'admin']),
  createCheckoutSession
);

/**
 * POST /api/billing/webhook
 * Stripe webhook endpoint för att hantera payment events
 * Denna route ska INTE ha authentication middleware
 */
router.post('/webhook', handleStripeWebhook);

/**
 * GET /api/billing/history
 * Hämtar billing history för organisationen
 */
router.get('/history',
  protect,
  tenantMiddleware,
  ensureSameOrganization,
  requireRole(['owner', 'admin']),
  getBillingHistory
);

/**
 * POST /api/billing/cancel
 * Avbryter nuvarande subscription
 */
router.post('/cancel',
  protect,
  tenantMiddleware,
  ensureSameOrganization,
  requireRole(['owner', 'admin']),
  cancelSubscription
);

/**
 * PUT /api/billing/subscription
 * Uppdaterar subscription (upgrade/downgrade)
 */
router.put('/subscription',
  protect,
  tenantMiddleware,
  ensureSameOrganization,
  requireRole(['owner', 'admin']),
  updateSubscription
);

/**
 * GET /api/billing/usage
 * Hämtar usage statistics för organisationen
 */
router.get('/usage',
  protect,
  tenantMiddleware,
  ensureSameOrganization,
  requireRole(['owner', 'admin']),
  getUsageStats
);

export default router;
