'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardHeader } from '@/components/dashboard/DashboardHeader';
import { BillingDashboard } from '@/components/billing/BillingDashboard';
import { SubscriptionPlans } from '@/components/billing/SubscriptionPlans';
import { BillingHistory } from '@/components/billing/BillingHistory';
import { UsageProgress } from '@/components/billing/UsageProgress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  TrendingUp, 
  Receipt, 
  Settings,
  AlertTriangle,
  Crown
} from 'lucide-react';
import { api } from '@/lib/api';
import { createCheckoutSession } from '@/lib/stripe';
import { toast } from 'sonner';

interface UsageStats {
  currentPlan: {
    id: string;
    name: string;
    price: number;
    currency: string;
  };
  usage: {
    analyses: { used: number; limit: number; percentage: number };
    storage: { used: number; limit: number; percentage: number };
    users: { used: number; limit: number; percentage: number };
  };
}

export default function BillingPage() {
  const { currentUser, authIsLoading } = useAuth();
  const router = useRouter();
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (!authIsLoading && !currentUser) {
      router.push('/login');
    }
  }, [authIsLoading, currentUser, router]);

  useEffect(() => {
    if (currentUser) {
      fetchUsageStats();
    }
  }, [currentUser]);

  const fetchUsageStats = async () => {
    try {
      const response = await api.get('/billing/usage');
      if (response.success) {
        setUsageStats(response.usage);
      }
    } catch (error) {
      console.error('Error fetching usage stats:', error);
      toast.error('Kunde inte hämta användningsstatistik');
    } finally {
      setLoading(false);
    }
  };

  const handlePlanUpgrade = () => {
    setActiveTab('plans');
  };

  const handlePlanSelect = async (planId: string) => {
    try {
      await createCheckoutSession(planId);
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Kunde inte starta checkout process');
    }
  };

  if (authIsLoading || loading) {
    return (
      <div className="space-y-6">
        <DashboardHeader title="Billing & Subscription" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                <div className="h-6 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-2 bg-slate-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return null;
  }

  const hasHighUsage = usageStats && (
    usageStats.usage.analyses.percentage >= 75 ||
    usageStats.usage.storage.percentage >= 75 ||
    usageStats.usage.users.percentage >= 75
  );

  return (
    <div className="space-y-6">
      <DashboardHeader 
        title="Billing & Subscription" 
        breadcrumbs={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Billing' }
        ]}
      />

      {/* High Usage Alert */}
      {hasHighUsage && (
        <Card className="border-yellow-200 bg-yellow-50/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-full">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-yellow-800">
                  Du närmar dig din användningsgräns
                </h3>
                <p className="text-sm text-yellow-700 mt-1">
                  Överväg att uppgradera din plan för att få mer kapacitet och undvika avbrott.
                </p>
              </div>
              <Button onClick={handlePlanUpgrade} className="bg-yellow-600 hover:bg-yellow-700">
                <Crown className="w-4 h-4 mr-2" />
                Uppgradera
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Översikt
          </TabsTrigger>
          <TabsTrigger value="plans" className="flex items-center gap-2">
            <Crown className="w-4 h-4" />
            Plans
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Receipt className="w-4 h-4" />
            Historik
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Inställningar
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <BillingDashboard />
          
          {usageStats && (
            <UsageProgress
              analyses={usageStats.usage.analyses}
              storage={usageStats.usage.storage}
              users={usageStats.usage.users}
              planName={usageStats.currentPlan.name}
              onUpgrade={handlePlanUpgrade}
            />
          )}
        </TabsContent>

        <TabsContent value="plans" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="w-5 h-5" />
                Välj din plan
              </CardTitle>
              <CardDescription>
                Uppgradera eller ändra din subscription plan för att få tillgång till fler funktioner
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SubscriptionPlans
                currentPlan={usageStats?.currentPlan.id}
                onPlanSelect={handlePlanSelect}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <BillingHistory />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Billing Inställningar
              </CardTitle>
              <CardDescription>
                Hantera dina betalningsmetoder och billing information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Betalningsmetoder</h3>
                  <p className="text-sm text-slate-600 mb-4">
                    Hantera dina sparade betalningsmetoder
                  </p>
                  <Button variant="outline">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Hantera betalningsmetoder
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Billing Email</h3>
                  <p className="text-sm text-slate-600 mb-4">
                    Email-adress för fakturor och billing-meddelanden
                  </p>
                  <Button variant="outline">
                    Uppdatera email
                  </Button>
                </div>

                <div className="p-4 border rounded-lg border-red-200 bg-red-50/50">
                  <h3 className="font-medium mb-2 text-red-800">Avbryt Subscription</h3>
                  <p className="text-sm text-red-700 mb-4">
                    Avbryt din nuvarande subscription. Du kommer att behålla tillgång till betalda funktioner till slutet av din nuvarande billing period.
                  </p>
                  <Button variant="destructive">
                    Avbryt Subscription
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
