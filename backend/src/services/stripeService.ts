import Stripe from 'stripe';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Initiera Stripe med secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  analysisLimit: number;
  storageLimit: number; // in bytes
  userLimit: number;
  features: string[];
}

// Definiera subscription plans
export const SUBSCRIPTION_PLANS: Record<string, SubscriptionPlan> = {
  free: {
    id: 'free',
    name: 'Free',
    price: 0,
    currency: 'sek',
    interval: 'month',
    analysisLimit: 10,
    storageLimit: 1073741824, // 1GB
    userLimit: 3,
    features: ['10 analyser per månad', '1GB lagring', '3 användare', 'Email support']
  },
  basic: {
    id: 'basic',
    name: 'Basic',
    price: 299,
    currency: 'sek',
    interval: 'month',
    analysisLimit: 100,
    storageLimit: 5368709120, // 5GB
    userLimit: 10,
    features: ['100 analyser per månad', '5GB lagring', '10 användare', 'Chat support', 'CSV/PDF export']
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    price: 899,
    currency: 'sek',
    interval: 'month',
    analysisLimit: 500,
    storageLimit: 21474836480, // 20GB
    userLimit: 50,
    features: ['500 analyser per månad', '20GB lagring', '50 användare', 'Priority support', 'API access', 'White-label']
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    price: 2499,
    currency: 'sek',
    interval: 'month',
    analysisLimit: -1, // Unlimited
    storageLimit: 107374182400, // 100GB
    userLimit: -1, // Unlimited
    features: ['Obegränsade analyser', '100GB lagring', 'Obegränsade användare', 'Dedicated support', 'Custom integrations', 'SLA']
  }
};

export class StripeService {
  
  /**
   * Skapar en Stripe customer för en organisation
   */
  async createCustomer(organizationId: string, email: string, name: string): Promise<Stripe.Customer> {
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        organizationId
      }
    });

    // Uppdatera organisation med Stripe customer ID
    await prisma.organization.update({
      where: { id: organizationId },
      data: {
        // Vi behöver lägga till stripeCustomerId i schema
        // stripeCustomerId: customer.id
      }
    });

    return customer;
  }

  /**
   * Skapar en subscription för en organisation
   */
  async createSubscription(
    organizationId: string, 
    planId: string, 
    paymentMethodId?: string
  ): Promise<{ subscription: Stripe.Subscription; clientSecret?: string }> {
    
    const plan = SUBSCRIPTION_PLANS[planId];
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }

    // Hämta organisation
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    // För free plan, skapa bara lokal subscription
    if (planId === 'free') {
      const subscription = await this.createLocalSubscription(organizationId, plan);
      return { subscription: subscription as any };
    }

    // Skapa eller hämta Stripe customer
    let customerId = organization.stripeCustomerId;
    if (!customerId) {
      const customer = await this.createCustomer(
        organizationId, 
        organization.billingEmail!, 
        organization.name
      );
      customerId = customer.id;
    }

    // Skapa Stripe price om det inte finns
    const priceId = await this.getOrCreatePrice(plan);

    // Skapa subscription i Stripe
    const subscriptionData: Stripe.SubscriptionCreateParams = {
      customer: customerId,
      items: [{ price: priceId }],
      metadata: {
        organizationId,
        planId
      },
      expand: ['latest_invoice.payment_intent']
    };

    // Lägg till payment method om det finns
    if (paymentMethodId) {
      subscriptionData.default_payment_method = paymentMethodId;
    }

    const stripeSubscription = await stripe.subscriptions.create(subscriptionData);

    // Skapa lokal subscription
    await this.createLocalSubscription(organizationId, plan, stripeSubscription.id);

    // Returnera client secret för payment confirmation om behövs
    let clientSecret: string | undefined;
    if (stripeSubscription.latest_invoice && typeof stripeSubscription.latest_invoice === 'object') {
      const paymentIntent = stripeSubscription.latest_invoice.payment_intent;
      if (paymentIntent && typeof paymentIntent === 'object') {
        clientSecret = paymentIntent.client_secret || undefined;
      }
    }

    return { subscription: stripeSubscription, clientSecret };
  }

  /**
   * Skapar eller hämtar Stripe price för en plan
   */
  private async getOrCreatePrice(plan: SubscriptionPlan): Promise<string> {
    // Först, försök hitta befintlig price
    const prices = await stripe.prices.list({
      lookup_keys: [plan.id],
      expand: ['data.product']
    });

    if (prices.data.length > 0) {
      return prices.data[0].id;
    }

    // Skapa product först
    const product = await stripe.products.create({
      name: `Flowlytics ${plan.name}`,
      description: `${plan.name} plan för Flowlytics`,
      metadata: {
        planId: plan.id
      }
    });

    // Skapa price
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: plan.price * 100, // Stripe använder ören
      currency: plan.currency,
      recurring: {
        interval: plan.interval
      },
      lookup_key: plan.id,
      metadata: {
        planId: plan.id
      }
    });

    return price.id;
  }

  /**
   * Skapar lokal subscription i databasen
   */
  private async createLocalSubscription(
    organizationId: string, 
    plan: SubscriptionPlan, 
    stripeSubscriptionId?: string
  ) {
    const now = new Date();
    const periodEnd = new Date();
    periodEnd.setMonth(periodEnd.getMonth() + (plan.interval === 'year' ? 12 : 1));

    return await prisma.subscription.create({
      data: {
        organizationId,
        tier: plan.id,
        status: 'active',
        currentPeriodStart: now,
        currentPeriodEnd: periodEnd,
        stripeSubscriptionId,
        stripePriceId: stripeSubscriptionId ? await this.getOrCreatePrice(plan) : undefined,
        analysisLimit: plan.analysisLimit,
        storageLimit: BigInt(plan.storageLimit),
        userLimit: plan.userLimit
      }
    });
  }

  /**
   * Hanterar Stripe webhooks
   */
  async handleWebhook(signature: string, payload: string): Promise<void> {
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;
    
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(payload, signature, endpointSecret);
    } catch (err: any) {
      throw new Error(`Webhook signature verification failed: ${err.message}`);
    }

    switch (event.type) {
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await this.handleSubscriptionChange(event.data.object as Stripe.Subscription);
        break;
      
      case 'invoice.payment_succeeded':
        await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      
      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  }

  /**
   * Hanterar subscription ändringar från Stripe
   */
  private async handleSubscriptionChange(subscription: Stripe.Subscription): Promise<void> {
    const organizationId = subscription.metadata.organizationId;
    if (!organizationId) return;

    const status = subscription.status === 'active' ? 'active' : 
                  subscription.status === 'past_due' ? 'past_due' : 'cancelled';

    await prisma.subscription.updateMany({
      where: { 
        organizationId,
        stripeSubscriptionId: subscription.id 
      },
      data: {
        status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000)
      }
    });

    // Uppdatera organisation status
    await prisma.organization.update({
      where: { id: organizationId },
      data: { subscriptionStatus: status }
    });
  }

  /**
   * Hanterar lyckad betalning
   */
  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    const subscription = invoice.subscription;
    if (!subscription || typeof subscription === 'string') return;

    const organizationId = subscription.metadata?.organizationId;
    if (!organizationId) return;

    // Uppdatera usage record med betalning
    const currentMonth = new Date().toISOString().slice(0, 7);
    await prisma.usageRecord.upsert({
      where: {
        organizationId_month: {
          organizationId,
          month: currentMonth
        }
      },
      update: {
        amount: invoice.amount_paid ? invoice.amount_paid / 100 : undefined,
        currency: invoice.currency?.toUpperCase() || 'SEK',
        billedAt: new Date()
      },
      create: {
        organizationId,
        month: currentMonth,
        amount: invoice.amount_paid ? invoice.amount_paid / 100 : 0,
        currency: invoice.currency?.toUpperCase() || 'SEK',
        billedAt: new Date()
      }
    });
  }

  /**
   * Hanterar misslyckad betalning
   */
  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    const subscription = invoice.subscription;
    if (!subscription || typeof subscription === 'string') return;

    const organizationId = subscription.metadata?.organizationId;
    if (!organizationId) return;

    // Uppdatera organisation status
    await prisma.organization.update({
      where: { id: organizationId },
      data: { subscriptionStatus: 'past_due' }
    });

    // TODO: Skicka email notification om misslyckad betalning
  }

  /**
   * Skapar en Stripe Checkout session
   */
  async createCheckoutSession(
    organizationId: string,
    planId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<Stripe.Checkout.Session> {
    
    const plan = SUBSCRIPTION_PLANS[planId];
    if (!plan || planId === 'free') {
      throw new Error('Invalid plan for checkout');
    }

    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    const priceId = await this.getOrCreatePrice(plan);

    return await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      customer_email: organization.billingEmail || undefined,
      metadata: {
        organizationId,
        planId
      },
      success_url: successUrl,
      cancel_url: cancelUrl,
      subscription_data: {
        metadata: {
          organizationId,
          planId
        }
      }
    });
  }

  /**
   * Hämtar alla tillgängliga plans
   */
  getAvailablePlans(): SubscriptionPlan[] {
    return Object.values(SUBSCRIPTION_PLANS);
  }
}
