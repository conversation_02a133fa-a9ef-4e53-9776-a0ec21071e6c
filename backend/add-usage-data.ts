import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addUsageData() {
  console.log('📊 Adding usage data for testing...');

  // Hämta organisationer
  const organizations = await prisma.organization.findMany();

  for (const org of organizations) {
    // Lägg till usage records för senaste 3 månaderna
    const months = ['2024-11', '2024-12', '2025-01'];
    
    for (const month of months) {
      await prisma.usageRecord.upsert({
        where: {
          organizationId_month: {
            organizationId: org.id,
            month: month
          }
        },
        update: {},
        create: {
          organizationId: org.id,
          month: month,
          amount: org.subscriptionTier === 'pro' ? 799 : 
                  org.subscriptionTier === 'basic' ? 299 : 0,
          currency: 'SEK',
          billedAt: new Date(`${month}-01`)
        }
      });
    }

    // Uppdatera storage usage för realistisk data
    const storageUsed = org.subscriptionTier === 'pro' ? BigInt(5 * 1024 * 1024 * 1024) : // 5GB
                       org.subscriptionTier === 'basic' ? BigInt(2 * 1024 * 1024 * 1024) : // 2GB  
                       BigInt(500 * 1024 * 1024); // 500MB

    const monthlyAnalyses = org.subscriptionTier === 'pro' ? 45 :
                           org.subscriptionTier === 'basic' ? 23 : 7;

    await prisma.organization.update({
      where: { id: org.id },
      data: {
        storageUsed: storageUsed,
        monthlyAnalyses: monthlyAnalyses
      }
    });

    console.log(`✅ Added usage data for ${org.name}`);
  }

  console.log('🎉 Usage data added successfully!');
}

addUsageData()
  .catch((e) => {
    console.error('❌ Failed to add usage data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
