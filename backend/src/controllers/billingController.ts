import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/authMiddleware.js';
import { StripeService } from '../services/stripeService.js';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
const stripeService = new StripeService();

/**
 * GET /api/billing/plans
 * Hämtar alla tillgängliga subscription plans
 */
export const getSubscriptionPlans = async (req: Request, res: Response) => {
  try {
    const plans = stripeService.getAvailablePlans();
    res.json({ success: true, plans });
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Kunde inte hämta subscription plans' 
    });
  }
};

/**
 * GET /api/billing/subscription
 * Hämtar organisationens nuvarande subscription
 */
export const getCurrentSubscription = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.organizationId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Organization ID saknas' 
      });
    }

    const subscription = await stripeService.getCurrentSubscription(req.user.organizationId);
    res.json({ success: true, subscription });
  } catch (error) {
    console.error('Error fetching current subscription:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Kunde inte hämta nuvarande subscription' 
    });
  }
};

/**
 * POST /api/billing/checkout
 * Skapar en Stripe Checkout session för subscription upgrade/change
 */
export const createCheckoutSession = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { planId } = req.body;
    
    if (!req.user?.organizationId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Organization ID saknas' 
      });
    }

    if (!planId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Plan ID krävs' 
      });
    }

    // Skapa success och cancel URLs
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const successUrl = `${baseUrl}/dashboard/billing/success?session_id={CHECKOUT_SESSION_ID}`;
    const cancelUrl = `${baseUrl}/dashboard/billing`;

    const session = await stripeService.createCheckoutSession(
      req.user.organizationId,
      planId,
      successUrl,
      cancelUrl
    );

    res.json({ 
      success: true, 
      sessionId: session.id,
      url: session.url 
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Kunde inte skapa checkout session' 
    });
  }
};

/**
 * POST /api/billing/webhook
 * Stripe webhook endpoint för att hantera payment events
 */
export const handleStripeWebhook = async (req: Request, res: Response) => {
  try {
    const signature = req.headers['stripe-signature'] as string;
    
    if (!signature) {
      return res.status(400).json({ 
        success: false, 
        message: 'Stripe signature saknas' 
      });
    }

    // Raw body behövs för webhook verification
    const payload = req.body;
    
    await stripeService.handleWebhook(signature, payload);
    
    res.json({ success: true, received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(400).json({ 
      success: false, 
      message: 'Webhook verification failed' 
    });
  }
};

/**
 * GET /api/billing/history
 * Hämtar billing history för organisationen
 */
export const getBillingHistory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.organizationId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Organization ID saknas' 
      });
    }

    const history = await stripeService.getBillingHistory(req.user.organizationId);
    res.json({ success: true, history });
  } catch (error) {
    console.error('Error fetching billing history:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Kunde inte hämta billing history' 
    });
  }
};

/**
 * POST /api/billing/cancel
 * Avbryter nuvarande subscription
 */
export const cancelSubscription = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.organizationId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Organization ID saknas' 
      });
    }

    await stripeService.cancelSubscription(req.user.organizationId);
    res.json({ 
      success: true, 
      message: 'Subscription avbruten framgångsrikt' 
    });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Kunde inte avbryta subscription' 
    });
  }
};

/**
 * PUT /api/billing/subscription
 * Uppdaterar subscription (upgrade/downgrade)
 */
export const updateSubscription = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { planId } = req.body;
    
    if (!req.user?.organizationId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Organization ID saknas' 
      });
    }

    if (!planId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Plan ID krävs' 
      });
    }

    const subscription = await stripeService.updateSubscription(
      req.user.organizationId, 
      planId
    );
    
    res.json({ 
      success: true, 
      subscription,
      message: 'Subscription uppdaterad framgångsrikt' 
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Kunde inte uppdatera subscription' 
    });
  }
};

/**
 * GET /api/billing/usage
 * Hämtar usage statistics för organisationen
 */
export const getUsageStats = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user?.organizationId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Organization ID saknas' 
      });
    }

    const usage = await stripeService.getUsageStats(req.user.organizationId);
    res.json({ success: true, usage });
  } catch (error) {
    console.error('Error fetching usage stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Kunde inte hämta usage statistics' 
    });
  }
};
