'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardHeader } from '@/components/dashboard/DashboardHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  ArrowRight, 
  CreditCard,
  Calendar,
  Crown,
  Sparkles
} from 'lucide-react';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface SubscriptionDetails {
  id: string;
  planId: string;
  status: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  plan: {
    name: string;
    price: number;
    currency: string;
    features: string[];
  };
}

export default function BillingSuccessPage() {
  const { currentUser, authIsLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  
  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!authIsLoading && !currentUser) {
      router.push('/login');
    }
  }, [authIsLoading, currentUser, router]);

  useEffect(() => {
    if (currentUser && sessionId) {
      // Vänta lite för att låta webhook processa
      setTimeout(() => {
        fetchSubscriptionDetails();
      }, 2000);
    } else if (currentUser && !sessionId) {
      // Om ingen session ID, gå tillbaka till billing
      router.push('/dashboard/billing');
    }
  }, [currentUser, sessionId, router]);

  const fetchSubscriptionDetails = async () => {
    try {
      const response = await api.get('/billing/subscription');
      if (response.success) {
        setSubscription(response.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription details:', error);
      toast.error('Kunde inte hämta subscription detaljer');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sv-SE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatPrice = (price: number, currency: string) => {
    return `${price} ${currency.toUpperCase()}/månad`;
  };

  if (authIsLoading || loading) {
    return (
      <div className="space-y-6">
        <DashboardHeader title="Betalning Genomförd" />
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p>Verifierar din betalning...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!currentUser) {
    return null;
  }

  return (
    <div className="space-y-6">
      <DashboardHeader 
        title="Betalning Genomförd" 
        breadcrumbs={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Billing', href: '/dashboard/billing' },
          { label: 'Framgång' }
        ]}
      />

      <div className="max-w-2xl mx-auto">
        {/* Success Message */}
        <Card className="border-green-200 bg-green-50/50">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold text-green-800 mb-2">
              Betalning Genomförd!
            </h1>
            <p className="text-green-700 mb-6">
              Tack för din betalning. Din subscription har aktiverats och du har nu tillgång till alla funktioner.
            </p>
            
            {subscription && (
              <div className="bg-white rounded-lg p-6 border border-green-200">
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Crown className="w-5 h-5 text-purple-600" />
                  <h2 className="text-lg font-semibold">{subscription.plan.name}</h2>
                  <Badge className="bg-green-100 text-green-800">Aktiv</Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <CreditCard className="w-4 h-4 text-slate-500" />
                    <span>Pris: {formatPrice(subscription.plan.price, subscription.plan.currency)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-slate-500" />
                    <span>Förnyas: {formatDate(subscription.currentPeriodEnd)}</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Plan Features */}
        {subscription && subscription.plan.features && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Dina nya funktioner
              </CardTitle>
              <CardDescription>
                Du har nu tillgång till alla dessa funktioner med din {subscription.plan.name} plan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {subscription.plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Next Steps */}
        <Card>
          <CardHeader>
            <CardTitle>Nästa steg</CardTitle>
            <CardDescription>
              Här är vad du kan göra nu när din subscription är aktiv
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={() => router.push('/dashboard')}
              className="w-full justify-between"
            >
              Börja analysera dina Excel-filer
              <ArrowRight className="w-4 h-4" />
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push('/dashboard/billing')}
              className="w-full justify-between"
            >
              Visa din billing dashboard
              <ArrowRight className="w-4 h-4" />
            </Button>
            
            <Button 
              variant="ghost"
              onClick={() => router.push('/dashboard/settings')}
              className="w-full justify-between"
            >
              Konfigurera dina inställningar
              <ArrowRight className="w-4 h-4" />
            </Button>
          </CardContent>
        </Card>

        {/* Support */}
        <Card className="border-blue-200 bg-blue-50/50">
          <CardContent className="p-6 text-center">
            <h3 className="font-medium text-blue-800 mb-2">Behöver du hjälp?</h3>
            <p className="text-sm text-blue-700 mb-4">
              Om du har några frågor om din subscription eller behöver hjälp att komma igång, tveka inte att kontakta oss.
            </p>
            <Button variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
              Kontakta Support
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
