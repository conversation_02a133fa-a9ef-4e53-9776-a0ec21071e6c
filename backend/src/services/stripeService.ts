import Stripe from 'stripe';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Validate Stripe configuration
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY;
if (!STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY environment variable is required');
  console.error('Please set STRIPE_SECRET_KEY in your .env file');
  console.error('Get your secret key from: https://dashboard.stripe.com/apikeys');
  throw new Error('Missing STRIPE_SECRET_KEY environment variable');
}

// Check if using placeholder key
const isPlaceholderKey = STRIPE_SECRET_KEY.includes('placeholder');
if (isPlaceholderKey) {
  console.warn('⚠️  Using placeholder Stripe key - replace with your actual Stripe test key');
  console.warn('   Get your test key from: https://dashboard.stripe.com/test/apikeys');
}

// Initiera Stripe med secret key
const stripe = new Stripe(STRIPE_SECRET_KEY, {
  apiVersion: '2024-06-20',
});

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  analysisLimit: number;
  storageLimit: number; // in bytes
  userLimit: number;
  features: string[];
}

// Definiera subscription plans
export const SUBSCRIPTION_PLANS: Record<string, SubscriptionPlan> = {
  free: {
    id: 'free',
    name: 'Free',
    price: 0,
    currency: 'sek',
    interval: 'month',
    analysisLimit: 10,
    storageLimit: 1073741824, // 1GB
    userLimit: 3,
    features: ['10 analyser per månad', '1GB lagring', '3 användare', 'Email support']
  },
  basic: {
    id: 'basic',
    name: 'Basic',
    price: 299,
    currency: 'sek',
    interval: 'month',
    analysisLimit: 100,
    storageLimit: 5368709120, // 5GB
    userLimit: 10,
    features: ['100 analyser per månad', '5GB lagring', '10 användare', 'Chat support', 'CSV/PDF export']
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    price: 899,
    currency: 'sek',
    interval: 'month',
    analysisLimit: 500,
    storageLimit: 21474836480, // 20GB
    userLimit: 50,
    features: ['500 analyser per månad', '20GB lagring', '50 användare', 'Priority support', 'API access', 'White-label']
  },
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    price: 2499,
    currency: 'sek',
    interval: 'month',
    analysisLimit: -1, // Unlimited
    storageLimit: 107374182400, // 100GB
    userLimit: -1, // Unlimited
    features: ['Obegränsade analyser', '100GB lagring', 'Obegränsade användare', 'Dedicated support', 'Custom integrations', 'SLA']
  }
};

export class StripeService {
  private isPlaceholderMode: boolean;

  constructor() {
    this.isPlaceholderMode = STRIPE_SECRET_KEY.includes('placeholder');
    if (this.isPlaceholderMode) {
      console.warn('🧪 StripeService running in placeholder mode - some features will be mocked');
    }
  }
  
  /**
   * Skapar en Stripe customer för en organisation
   */
  async createCustomer(organizationId: string, email: string, name: string): Promise<Stripe.Customer> {
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        organizationId
      }
    });

    // Uppdatera organisation med Stripe customer ID
    await prisma.organization.update({
      where: { id: organizationId },
      data: {
        // Vi behöver lägga till stripeCustomerId i schema
        // stripeCustomerId: customer.id
      }
    });

    return customer;
  }

  /**
   * Skapar en subscription för en organisation
   */
  async createSubscription(
    organizationId: string, 
    planId: string, 
    paymentMethodId?: string
  ): Promise<{ subscription: Stripe.Subscription; clientSecret?: string }> {
    
    const plan = SUBSCRIPTION_PLANS[planId];
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }

    // Hämta organisation
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    // För free plan, skapa bara lokal subscription
    if (planId === 'free') {
      const subscription = await this.createLocalSubscription(organizationId, plan);
      return { subscription: subscription as any };
    }

    // Skapa eller hämta Stripe customer
    let customerId = organization.stripeCustomerId;
    if (!customerId) {
      const customer = await this.createCustomer(
        organizationId, 
        organization.billingEmail!, 
        organization.name
      );
      customerId = customer.id;
    }

    // Skapa Stripe price om det inte finns
    const priceId = await this.getOrCreatePrice(plan);

    // Skapa subscription i Stripe
    const subscriptionData: Stripe.SubscriptionCreateParams = {
      customer: customerId,
      items: [{ price: priceId }],
      metadata: {
        organizationId,
        planId
      },
      expand: ['latest_invoice.payment_intent']
    };

    // Lägg till payment method om det finns
    if (paymentMethodId) {
      subscriptionData.default_payment_method = paymentMethodId;
    }

    const stripeSubscription = await stripe.subscriptions.create(subscriptionData);

    // Skapa lokal subscription
    await this.createLocalSubscription(organizationId, plan, stripeSubscription.id);

    // Returnera client secret för payment confirmation om behövs
    let clientSecret: string | undefined;
    if (stripeSubscription.latest_invoice && typeof stripeSubscription.latest_invoice === 'object') {
      const paymentIntent = stripeSubscription.latest_invoice.payment_intent;
      if (paymentIntent && typeof paymentIntent === 'object') {
        clientSecret = paymentIntent.client_secret || undefined;
      }
    }

    return { subscription: stripeSubscription, clientSecret };
  }

  /**
   * Skapar eller hämtar Stripe price för en plan
   */
  private async getOrCreatePrice(plan: SubscriptionPlan): Promise<string> {
    // Först, försök hitta befintlig price
    const prices = await stripe.prices.list({
      lookup_keys: [plan.id],
      expand: ['data.product']
    });

    if (prices.data.length > 0) {
      return prices.data[0].id;
    }

    // Skapa product först
    const product = await stripe.products.create({
      name: `Flowlytics ${plan.name}`,
      description: `${plan.name} plan för Flowlytics`,
      metadata: {
        planId: plan.id
      }
    });

    // Skapa price
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: plan.price * 100, // Stripe använder ören
      currency: plan.currency,
      recurring: {
        interval: plan.interval
      },
      lookup_key: plan.id,
      metadata: {
        planId: plan.id
      }
    });

    return price.id;
  }

  /**
   * Skapar lokal subscription i databasen
   */
  private async createLocalSubscription(
    organizationId: string, 
    plan: SubscriptionPlan, 
    stripeSubscriptionId?: string
  ) {
    const now = new Date();
    const periodEnd = new Date();
    periodEnd.setMonth(periodEnd.getMonth() + (plan.interval === 'year' ? 12 : 1));

    return await prisma.subscription.create({
      data: {
        organizationId,
        tier: plan.id,
        status: 'active',
        currentPeriodStart: now,
        currentPeriodEnd: periodEnd,
        stripeSubscriptionId,
        stripePriceId: stripeSubscriptionId ? await this.getOrCreatePrice(plan) : undefined,
        analysisLimit: plan.analysisLimit,
        storageLimit: BigInt(plan.storageLimit),
        userLimit: plan.userLimit
      }
    });
  }

  /**
   * Hanterar Stripe webhooks
   */
  async handleWebhook(signature: string, payload: string): Promise<void> {
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!endpointSecret) {
      console.error('❌ STRIPE_WEBHOOK_SECRET environment variable is required for webhook verification');
      throw new Error('Missing STRIPE_WEBHOOK_SECRET environment variable');
    }

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(payload, signature, endpointSecret);
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message);
      throw new Error(`Webhook signature verification failed: ${err.message}`);
    }

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await this.handleSubscriptionChange(event.data.object as Stripe.Subscription);
        break;

      case 'invoice.payment_succeeded':
        await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
        break;

      case 'checkout.session.completed':
        await this.handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
        break;

      case 'customer.subscription.trial_will_end':
        await this.handleTrialWillEnd(event.data.object as Stripe.Subscription);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  }

  /**
   * Hanterar subscription ändringar från Stripe
   */
  private async handleSubscriptionChange(subscription: Stripe.Subscription): Promise<void> {
    const organizationId = subscription.metadata.organizationId;
    if (!organizationId) return;

    const status = subscription.status === 'active' ? 'active' : 
                  subscription.status === 'past_due' ? 'past_due' : 'cancelled';

    await prisma.subscription.updateMany({
      where: { 
        organizationId,
        stripeSubscriptionId: subscription.id 
      },
      data: {
        status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000)
      }
    });

    // Uppdatera organisation status
    await prisma.organization.update({
      where: { id: organizationId },
      data: { subscriptionStatus: status }
    });
  }

  /**
   * Hanterar lyckad betalning
   */
  private async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    const subscription = invoice.subscription;
    if (!subscription || typeof subscription === 'string') return;

    const organizationId = subscription.metadata?.organizationId;
    if (!organizationId) return;

    // Uppdatera usage record med betalning
    const currentMonth = new Date().toISOString().slice(0, 7);
    await prisma.usageRecord.upsert({
      where: {
        organizationId_month: {
          organizationId,
          month: currentMonth
        }
      },
      update: {
        amount: invoice.amount_paid ? invoice.amount_paid / 100 : undefined,
        currency: invoice.currency?.toUpperCase() || 'SEK',
        billedAt: new Date()
      },
      create: {
        organizationId,
        month: currentMonth,
        amount: invoice.amount_paid ? invoice.amount_paid / 100 : 0,
        currency: invoice.currency?.toUpperCase() || 'SEK',
        billedAt: new Date()
      }
    });
  }

  /**
   * Hanterar misslyckad betalning
   */
  private async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    const subscription = invoice.subscription;
    if (!subscription || typeof subscription === 'string') return;

    const organizationId = subscription.metadata?.organizationId;
    if (!organizationId) return;

    // Uppdatera organisation status
    await prisma.organization.update({
      where: { id: organizationId },
      data: { subscriptionStatus: 'past_due' }
    });

    // TODO: Skicka email notification om misslyckad betalning
  }

  /**
   * Hanterar slutförd checkout session
   */
  private async handleCheckoutCompleted(session: Stripe.Checkout.Session): Promise<void> {
    const organizationId = session.metadata?.organizationId;
    const planId = session.metadata?.planId;

    if (!organizationId || !planId) return;

    // Hämta subscription från session
    if (session.subscription && typeof session.subscription === 'string') {
      const subscription = await stripe.subscriptions.retrieve(session.subscription);
      await this.handleSubscriptionChange(subscription);
    }

    // Uppdatera organisation status
    await prisma.organization.update({
      where: { id: organizationId },
      data: {
        subscriptionStatus: 'active',
        subscriptionTier: planId
      }
    });
  }

  /**
   * Hanterar trial period som snart slutar
   */
  private async handleTrialWillEnd(subscription: Stripe.Subscription): Promise<void> {
    const organizationId = subscription.metadata?.organizationId;
    if (!organizationId) return;

    // TODO: Skicka email notification om trial som snart slutar
    console.log(`Trial will end for organization: ${organizationId}`);
  }

  /**
   * Skapar en Stripe Checkout session
   */
  async createCheckoutSession(
    organizationId: string,
    planId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<Stripe.Checkout.Session> {

    // Handle placeholder mode
    if (this.isPlaceholderMode) {
      console.warn('🧪 createCheckoutSession called in placeholder mode - returning mock session');
      return {
        id: 'cs_mock_session_id',
        url: `${successUrl}?session_id=cs_mock_session_id`,
        mode: 'subscription',
        status: 'open'
      } as Stripe.Checkout.Session;
    }

    const plan = SUBSCRIPTION_PLANS[planId];
    if (!plan || planId === 'free') {
      throw new Error('Invalid plan for checkout');
    }

    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    const priceId = await this.getOrCreatePrice(plan);

    return await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      customer_email: organization.billingEmail || undefined,
      metadata: {
        organizationId,
        planId
      },
      success_url: successUrl,
      cancel_url: cancelUrl,
      subscription_data: {
        metadata: {
          organizationId,
          planId
        }
      }
    });
  }

  /**
   * Hämtar alla tillgängliga plans
   */
  getAvailablePlans(): SubscriptionPlan[] {
    return Object.values(SUBSCRIPTION_PLANS);
  }

  /**
   * Hämtar nuvarande subscription för en organisation
   */
  async getCurrentSubscription(organizationId: string) {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        subscription: true
      }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    // Om det finns en Stripe subscription, hämta den från Stripe
    if (organization.stripeCustomerId && organization.subscription?.stripeSubscriptionId) {
      try {
        const stripeSubscription = await stripe.subscriptions.retrieve(
          organization.subscription.stripeSubscriptionId
        );

        return {
          ...organization.subscription,
          stripeData: stripeSubscription,
          plan: SUBSCRIPTION_PLANS[organization.subscription.planId]
        };
      } catch (error) {
        console.error('Error fetching Stripe subscription:', error);
      }
    }

    return {
      ...organization.subscription,
      plan: organization.subscription ? SUBSCRIPTION_PLANS[organization.subscription.planId] : SUBSCRIPTION_PLANS.free
    };
  }

  /**
   * Hämtar billing history för en organisation
   */
  async getBillingHistory(organizationId: string) {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    // Hämta från lokal databas
    const localHistory = await prisma.usageRecord.findMany({
      where: { organizationId },
      orderBy: { month: 'desc' },
      take: 12 // Senaste 12 månaderna
    });

    // Om det finns Stripe customer, hämta även från Stripe
    let stripeHistory = [];
    if (organization.stripeCustomerId) {
      try {
        const invoices = await stripe.invoices.list({
          customer: organization.stripeCustomerId,
          limit: 12
        });
        stripeHistory = invoices.data;
      } catch (error) {
        console.error('Error fetching Stripe invoices:', error);
      }
    }

    return {
      local: localHistory,
      stripe: stripeHistory
    };
  }

  /**
   * Avbryter subscription för en organisation
   */
  async cancelSubscription(organizationId: string) {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { subscription: true }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    // Avbryt i Stripe om det finns en Stripe subscription
    if (organization.subscription?.stripeSubscriptionId) {
      await stripe.subscriptions.cancel(organization.subscription.stripeSubscriptionId);
    }

    // Uppdatera lokal subscription till free plan
    await this.createLocalSubscription(organizationId, SUBSCRIPTION_PLANS.free);

    // Uppdatera organisation
    await prisma.organization.update({
      where: { id: organizationId },
      data: {
        subscriptionStatus: 'cancelled',
        subscriptionTier: 'free'
      }
    });
  }

  /**
   * Uppdaterar subscription för en organisation
   */
  async updateSubscription(organizationId: string, newPlanId: string) {
    const plan = SUBSCRIPTION_PLANS[newPlanId];
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }

    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: { subscription: true }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    // För free plan, bara uppdatera lokalt
    if (newPlanId === 'free') {
      await this.createLocalSubscription(organizationId, plan);
      return { success: true, plan };
    }

    // För betalda plans, hantera via Stripe
    if (organization.subscription?.stripeSubscriptionId) {
      // Uppdatera befintlig subscription
      const priceId = await this.getOrCreatePrice(plan);

      const stripeSubscription = await stripe.subscriptions.update(
        organization.subscription.stripeSubscriptionId,
        {
          items: [{
            id: organization.subscription.stripeSubscriptionId,
            price: priceId
          }],
          metadata: {
            organizationId,
            planId: newPlanId
          }
        }
      );

      await this.createLocalSubscription(organizationId, plan, stripeSubscription.id);
      return { success: true, plan, stripeSubscription };
    } else {
      // Skapa ny subscription
      return await this.createSubscription(organizationId, newPlanId);
    }
  }

  /**
   * Hämtar usage statistics för en organisation
   */
  async getUsageStats(organizationId: string) {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        subscription: true,
        analyses: {
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        },
        users: true
      }
    });

    if (!organization) {
      throw new Error('Organization not found');
    }

    const currentMonth = new Date().toISOString().slice(0, 7);
    const usageRecord = await prisma.usageRecord.findUnique({
      where: {
        organizationId_month: {
          organizationId,
          month: currentMonth
        }
      }
    });

    const plan = organization.subscription ?
      SUBSCRIPTION_PLANS[organization.subscription.planId] :
      SUBSCRIPTION_PLANS.free;

    return {
      currentPlan: plan,
      usage: {
        analyses: {
          used: organization.analyses.length,
          limit: plan.analysisLimit,
          percentage: Math.round((organization.analyses.length / plan.analysisLimit) * 100)
        },
        storage: {
          used: Number(organization.storageUsed),
          limit: plan.storageLimit,
          percentage: Math.round((Number(organization.storageUsed) / plan.storageLimit) * 100)
        },
        users: {
          used: organization.users.length,
          limit: plan.userLimit,
          percentage: Math.round((organization.users.length / plan.userLimit) * 100)
        }
      },
      billingInfo: usageRecord
    };
  }
}
